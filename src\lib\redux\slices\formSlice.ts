import { FormScreen } from "@/components/types";
import {
  AddFormElementPayload,
  AddFormSectionPayload,
  FormState,
  RemoveFormElementPayload,
  RemoveFormSectionPayload,
  ReplaceFormElementPayload,
  ReplaceScreenSectionsPayload,
  ReplaceSectionElementsPayload,
  SelectedFormElementPayload,
  UpdateScreenDetailsPayload,
  UpdateSectionTitlePayload,
} from "@/lib/redux/types";
import { createSlice } from "@reduxjs/toolkit";

const initialState: FormState = {
  formScreens: [],
  formId: "",
  selectedFormBuilderItem: null,
  selectedFormBuilderItemScreen: "",
};

const formSlice = createSlice({
  name: "form",
  initialState,
  reducers: {
    addFormElement: (state, { payload }: { payload: AddFormElementPayload }) => {
      const { screenId, sectionId, elementIndex, element } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      const sectionIndex = state.formScreens[screenIndex].sections.findIndex(section => section.id === sectionId);
      const currentSection = state.formScreens[screenIndex].sections[sectionIndex];
      const elementExists = currentSection.elements.some(el => el.id === element.id);
      if (elementExists) return;
      currentSection.elements.splice(elementIndex, 0, element);
    },

    addFormSection: (state, { payload }: { payload: AddFormSectionPayload }) => {
      const { screenId, sectionIndex, section } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      const currentScreen = state.formScreens[screenIndex];
      const sectionExists = currentScreen.sections.some(s => s.id === section.id);
      if (sectionExists) return;
      currentScreen.sections.splice(sectionIndex, 0, section);
    },

    removeFormElement: (state, { payload }: { payload: RemoveFormElementPayload }) => {
      const { screenId, sectionId, elementId } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      const sectionIndex = state.formScreens[screenIndex].sections.findIndex(section => section.id === sectionId);
      state.formScreens[screenIndex].sections[sectionIndex].elements = state.formScreens[screenIndex].sections[sectionIndex].elements.filter(
        element => element.id !== elementId,
      );
    },

    removeFormSection: (state, { payload }: { payload: RemoveFormSectionPayload }) => {
      const { screenId, sectionId } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      state.formScreens[screenIndex].sections = state.formScreens[screenIndex].sections.filter(section => section.id !== sectionId);
    },

    replaceFormScreens: (state, { payload }: { payload: FormScreen[] }) => {
      state.formScreens = payload;
    },

    replaceFormElement: (state, { payload }: { payload: ReplaceFormElementPayload }) => {
      const { screenId, sectionId, element } = payload;
      console.log(payload);
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      const sectionIndex = state.formScreens[screenIndex].sections.findIndex(section => section.id === sectionId);
      const elementIndex = state.formScreens[screenIndex].sections[sectionIndex].elements.findIndex(el => el.id === element.id);
      state.formScreens[screenIndex].sections[sectionIndex].elements[elementIndex] = element;
    },

    replaceSectionElements: (state, { payload }: { payload: ReplaceSectionElementsPayload }) => {
      const { screenId, sectionId, elements } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      const sectionIndex = state.formScreens[screenIndex].sections.findIndex(section => section.id === sectionId);
      state.formScreens[screenIndex].sections[sectionIndex].elements = elements;
    },

    replaceScreenSections: (state, { payload }: { payload: ReplaceScreenSectionsPayload }) => {
      const { screenId, sections } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      state.formScreens[screenIndex].sections = sections;
    },

    updateFormId: (state, { payload }: { payload: string }) => {
      state.formId = payload;
    },

    updateSelectedFormBuilderItem: (state, { payload }: { payload: SelectedFormElementPayload | null }) => {
      state.selectedFormBuilderItem = payload;
    },

    updateSelectedFormBuilderItemScreen: (state, { payload }: { payload: string }) => {
      state.selectedFormBuilderItemScreen = payload;
    },

    updateScreenDetails: (state, { payload }: { payload: UpdateScreenDetailsPayload }) => {
      const { screenId, details, detailType } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      state.formScreens[screenIndex][detailType === "name" ? "name" : "description"] = details;
    },

    updateSectionTitle: (state, { payload }: { payload: UpdateSectionTitlePayload }) => {
      const { screenId, sectionId, title } = payload;
      const screenIndex = state.formScreens.findIndex(screen => screen.id === screenId);
      const sectionIndex = state.formScreens[screenIndex].sections.findIndex(section => section.id === sectionId);
      state.formScreens[screenIndex].sections[sectionIndex].title = title;
    },
  },
});

export const {
  addFormElement,
  addFormSection,
  removeFormElement,
  removeFormSection,
  replaceFormScreens,
  replaceSectionElements,
  replaceScreenSections,
  replaceFormElement,
  updateFormId,
  updateSelectedFormBuilderItem,
  updateSelectedFormBuilderItemScreen,
  updateScreenDetails,
  updateSectionTitle,
} = formSlice.actions;
export default formSlice.reducer;
