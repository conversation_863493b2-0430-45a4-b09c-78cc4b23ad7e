import NumberInput from "@/components/NumberInput";
import { ParentAnswerFieldProps } from "@/components/types";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const ParentAnswerField = ({ logicIndex, conditionIndex, type, options, form }: ParentAnswerFieldProps) => {
  const isChoiceField = ["SingleChoiceField", "MultipleChoiceField", "DropdownField"].includes(type);

  const currentOperator = form.watch(`logics.${logicIndex}.conditions.${conditionIndex}.operator`);

  if (currentOperator === "is in range") {
    return (
      <div className="flex gap-2">
        <FormField
          control={form.control}
          name={`logics.${logicIndex}.conditions.${conditionIndex}.minimumValue`}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <NumberInput value={field.value} placeholder="Min Number" onChange={field.onChange} disabled={!currentOperator} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={`logics.${logicIndex}.conditions.${conditionIndex}.maximumValue`}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormControl>
                <NumberInput value={field.value} placeholder="Max Number" onChange={field.onChange} disabled={!currentOperator} />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    );
  }

  return (
    <FormField
      control={form.control}
      name={`logics.${logicIndex}.conditions.${conditionIndex}.value`}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            {isChoiceField ? (
              <Select onValueChange={field.onChange} value={field.value} disabled={!currentOperator}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Option" />
                </SelectTrigger>
                <SelectContent>
                  {options.map(option => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <NumberInput {...field} placeholder="Enter value" onChange={field.onChange} disabled={!currentOperator} />
            )}
          </FormControl>
        </FormItem>
      )}
    />
  );
};

export default ParentAnswerField;
