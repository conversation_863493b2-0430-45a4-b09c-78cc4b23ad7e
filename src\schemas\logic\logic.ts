import * as z from "zod";
const conditionSchema = z.object({
  id: z.string(),
  componentId: z.string().optional(),
  operator: z.string().optional(),
  value: z.string().optional(),
  minimumValue: z.string().optional(),
  maximumValue: z.string().optional(),
  logic: z.string().nullable(),
});

const logicSchema = z.object({
  id: z.string(),
  conditions: z.array(conditionSchema).optional(),
  action: z.string().optional(),
});

export const elementLogicSchema = z.object({
  logics: z.array(logicSchema).min(1, "At least one logic is required"),
});
